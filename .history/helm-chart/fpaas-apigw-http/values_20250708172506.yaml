# Default values for fpaas-apigw-http
# This is a YAML-formatted file.

# Application configuration
app:
  name: fpaas-apigw-http
  version: "2.6.1"
  profile: fat  # Using fat profile as requested

# Image configuration
image:
  repository: your-registry/fpaas-apigw-http
  pullPolicy: IfNotPresent
  tag: "0.0.1-alpha"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

# Service Account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod Security Context
podSecurityContext:
  fsGroup: 1000

# Container Security Context
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: 1000

# Service configuration
service:
  type: ClusterIP
  port: 8084
  targetPort: 8084
  annotations: {}

# Ingress configuration
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: fpaas-apigw.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Resource limits and requests
resources:
  limits:
    cpu: "1000m"
    memory: "1Gi"
  requests:
    cpu: "100m"
    memory: "128Mi"

# JVM configuration
jvm:
  xms: "1024m"
  xmx: "1024m"
  xmn: "256m"
  metaspaceSize: "512m"
  maxMetaspaceSize: "1024m"

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Pod configuration
replicaCount: 1
podAnnotations: {}
podLabels: {}

# Node selection
nodeSelector: {}
tolerations: []
affinity: {}

# Probes configuration
livenessProbe:
  httpGet:
    path: /actuator/health
    port: 8084
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health
    port: 8084
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Persistent Volume for logs
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 10Gi
  mountPath: /app/logs

# External dependencies configuration
# These should be configured to match your fat environment
external:
  mysql:
    host: "onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com"
    port: 3306
    database: "fpaas"
    username: "fpaas"
    # Password should be set via secret
  
  redis:
    host: "redis-host"
    port: 6379
    # Password should be set via secret
  
  nacos:
    host: "nacos-host"
    port: 8848
    namespace: "hca0m5ezga95_xkf6m5ezgqn9"
  
  kafka:
    host: "kafka-host"
    port: 8092

# Secrets configuration
secrets:
  mysql:
    password: "1q2w3e4R!@#$"  # Should be base64 encoded in actual deployment
  redis:
    password: "Dcits123!@#"   # Should be base64 encoded in actual deployment

# ConfigMap data
config:
  # Additional application properties can be added here
  tokenRefreshTime: 30000
  tokenTimeout: *********
  httpMaxSize: ********
