# Custom values for FAT environment deployment
# Override default values for production-like fat environment

# Application configuration
app:
  name: fpaas-apigw-http
  version: "2.6.1"
  profile: fat  # Using fat profile as requested

# Image configuration - Update with your actual registry
image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/fpaas-apigw-http
  pullPolicy:   # Always pull latest for fat environment
  tag: "2.6.1"

# Increase replicas for fat environment
replicaCount: 2

# Resource configuration for fat environment
resources:
  limits:
    cpu: 3000m
    memory: 3Gi
  requests:
    cpu: 1000m
    memory: 1.5Gi

# JVM configuration optimized for fat environment
jvm:
  xms: "1536m"
  xmx: "1536m"
  xmn: "384m"
  metaspaceSize: "512m"
  maxMetaspaceSize: "1024m"

# Enable autoscaling for fat environment
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 8
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 75

# Service configuration
service:
  type: ClusterIP
  port: 8084
  targetPort: 8084
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"  # If using AWS

# Ingress configuration for fat environment
ingress:
  enabled: true
  className: "nginx"  # or your ingress class
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "300m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
  hosts:
    - host: fpaas-apigw-fat.yourdomain.com  # Update with your domain
      paths:
        - path: /
          pathType: Prefix
  tls: []
  # Uncomment and configure if you have TLS certificates
  # tls:
  #   - secretName: fpaas-apigw-tls
  #     hosts:
  #       - fpaas-apigw-fat.yourdomain.com

# External dependencies configuration for fat environment
external:
  mysql:
    host: "onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com"
    port: 3306
    database: "fpaas"
    username: "fpaas"
  
  redis:
    host: "redis-host"  # Update with your Redis host
    port: 6379
  
  nacos:
    host: "nacos-host"  # Update with your Nacos host
    port: 8848
    namespace: "hca0m5ezga95_xkf6m5ezgqn9"
  
  kafka:
    host: "kafka-host"  # Update with your Kafka host
    port: 8092

# Secrets configuration - These should be properly encoded in production
secrets:
  mysql:
    password: "1q2w3e4R!@#$"  # This will be base64 encoded automatically
  redis:
    password: "Dcits123!@#"   # This will be base64 encoded automatically

# Enhanced persistence for fat environment
persistence:
  enabled: true
  storageClass: "gp2"  # Update with your storage class
  accessMode: ReadWriteOnce
  size: 20Gi  # Increased size for fat environment
  mountPath: /app/logs

# Enhanced probes configuration
livenessProbe:
  httpGet:
    path: /actuator/health
    port: 8084
  initialDelaySeconds: 180  # Increased for fat environment
  periodSeconds: 30
  timeoutSeconds: 15
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health
    port: 8084
  initialDelaySeconds: 90
  periodSeconds: 15
  timeoutSeconds: 10
  failureThreshold: 3

# Pod configuration
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8084"
  prometheus.io/path: "/actuator/prometheus"

podLabels:
  environment: "fat"
  tier: "gateway"

# Node selection for fat environment
nodeSelector:
  # Uncomment and configure if you have specific node requirements
  # kubernetes.io/arch: amd64
  # node-type: application

# Tolerations for fat environment
tolerations: []
# - key: "dedicated"
#   operator: "Equal"
#   value: "application"
#   effect: "NoSchedule"

# Affinity rules for fat environment
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - fpaas-apigw-http
        topologyKey: kubernetes.io/hostname

# Additional configuration for fat environment
config:
  tokenRefreshTime: 30000
  tokenTimeout: 300000000
  httpMaxSize: 10485760

# Security context
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: 1000

podSecurityContext:
  fsGroup: 1000
  runAsNonRoot: true
  runAsUser: 1000
