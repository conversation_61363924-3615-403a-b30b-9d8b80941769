#!/bin/bash

# FPAAS API Gateway HTTP Deployment Script
# This script helps deploy the application using Helm

set -e

# Configuration
CHART_NAME="fpaas-apigw-http"
RELEASE_NAME="fpaas-apigw-http"
NAMESPACE="onebank"
CHART_PATH="./fpaas-apigw-http"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if helm is installed
check_helm() {
    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed. Please install He<PERSON> first."
        exit 1
    fi
    log_info "Helm version: $(helm version --short)"
}

# Check if kubectl is installed and configured
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "kubectl is not configured or cluster is not accessible."
        exit 1
    fi
    
    log_info "Connected to cluster: $(kubectl config current-context)"
}

# Create namespace if it doesn't exist
create_namespace() {
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "Creating namespace: $NAMESPACE"
        kubectl create namespace "$NAMESPACE"
    else
        log_info "Namespace $NAMESPACE already exists"
    fi
}

# Validate chart
validate_chart() {
    log_info "Validating Helm chart..."
    helm lint "$CHART_PATH"
    if [ $? -eq 0 ]; then
        log_info "Chart validation passed"
    else
        log_error "Chart validation failed"
        exit 1
    fi
}

# Deploy or upgrade
deploy() {
    local values_file=""
    
    # Check if custom values file exists
    if [ -f "values-production.yaml" ]; then
        values_file="-f values-production.yaml"
        log_info "Using custom values file: values-production.yaml"
    elif [ -f "values-fat.yaml" ]; then
        values_file="-f values-fat.yaml"
        log_info "Using custom values file: values-fat.yaml"
    fi
    
    # Check if release exists
    if helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME"; then
        log_info "Upgrading existing release: $RELEASE_NAME"
        helm upgrade "$RELEASE_NAME" "$CHART_PATH" \
            --namespace "$NAMESPACE" \
            $values_file \
            --wait \
            --timeout=10m
    else
        log_info "Installing new release: $RELEASE_NAME"
        helm install "$RELEASE_NAME" "$CHART_PATH" \
            --namespace "$NAMESPACE" \
            $values_file \
            --wait \
            --timeout=10m \
            --create-namespace
    fi
}

# Check deployment status
check_status() {
    log_info "Checking deployment status..."
    
    # Check Helm release status
    helm status "$RELEASE_NAME" -n "$NAMESPACE"
    
    # Check pod status
    log_info "Pod status:"
    kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/name=$CHART_NAME"
    
    # Check service status
    log_info "Service status:"
    kubectl get svc -n "$NAMESPACE" -l "app.kubernetes.io/name=$CHART_NAME"
}

# Show logs
show_logs() {
    log_info "Recent logs:"
    kubectl logs -n "$NAMESPACE" -l "app.kubernetes.io/name=$CHART_NAME" --tail=50
}

# Rollback function
rollback() {
    local revision=${1:-0}  # Default to previous revision
    log_warn "Rolling back to revision $revision"
    helm rollback "$RELEASE_NAME" $revision -n "$NAMESPACE"
}

# Uninstall function
uninstall() {
    log_warn "Uninstalling release: $RELEASE_NAME"
    helm uninstall "$RELEASE_NAME" -n "$NAMESPACE"
}

# Main script
main() {
    case "${1:-deploy}" in
        "deploy")
            log_info "Starting deployment process..."
            check_helm
            check_kubectl
            create_namespace
            validate_chart
            deploy
            check_status
            log_info "Deployment completed successfully!"
            ;;
        "status")
            check_kubectl
            check_status
            ;;
        "logs")
            check_kubectl
            show_logs
            ;;
        "rollback")
            check_kubectl
            rollback "$2"
            ;;
        "uninstall")
            check_kubectl
            uninstall
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [COMMAND]"
            echo ""
            echo "Commands:"
            echo "  deploy     Deploy or upgrade the application (default)"
            echo "  status     Check deployment status"
            echo "  logs       Show recent application logs"
            echo "  rollback   Rollback to previous version"
            echo "  uninstall  Uninstall the application"
            echo "  help       Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 deploy"
            echo "  $0 status"
            echo "  $0 logs"
            echo "  $0 rollback"
            echo "  $0 rollback 2  # Rollback to specific revision"
            echo "  $0 uninstall"
            ;;
        *)
            log_error "Unknown command: $1"
            echo "Use '$0 help' for usage information."
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
