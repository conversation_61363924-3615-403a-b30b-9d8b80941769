# Multi-stage build for FPAAS API Gateway HTTP
FROM openjdk:11-jre-alpine AS base

# Install required packages
RUN apk add --no-cache \
    bash \
    curl \
    tzdata \
    && rm -rf /var/cache/apk/*

# Set timezone
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create application user
RUN addgroup -g 1000 appuser && \
    adduser -D -u 1000 -G appuser appuser

# Create application directory
RUN mkdir -p /app/config /app/lib /app/logs && \
    chown -R appuser:appuser /app

# Set working directory
WORKDIR /app

# Copy application files
COPY --chown=appuser:appuser fpaas-apigw-http-2.6.1.jar /app/app.jar
COPY --chown=appuser:appuser lib/ /app/lib/
COPY --chown=appuser:appuser config/ /app/config/

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8084

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:8084/actuator/health || exit 1

# JVM options
ENV JAVA_OPTS="-server -Xms1024m -Xmx1024m -Xmn256m -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1024m -XX:-OmitStackTraceInFastThrow"

# Spring profile
ENV SPRING_PROFILES_ACTIVE=fat

# Start command
CMD ["sh", "-c", "java $JAVA_OPTS -Djava.ext.dirs=/app:/app/lib:$JAVA_HOME/jre/lib/ext -Dspring.profiles.active=$SPRING_PROFILES_ACTIVE -jar /app/app.jar"]
