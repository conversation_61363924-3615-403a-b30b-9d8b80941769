#!/bin/bash

# Build and push Docker image for FPAAS API Gateway HTTP

set -e

# Configuration
IMAGE_NAME="fpaas-apigw-http"
IMAGE_TAG="2.6.1"
REGISTRY="164104811622.dkr.ecr.ap-southeast-1.amazonaws.com"  # Update with your registry
FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    log_info "Docker is running"
}

# Build Docker image
build_image() {
    log_info "Building Docker image: $FULL_IMAGE_NAME"
    
    docker build \
        --tag "$FULL_IMAGE_NAME" \
        --tag "${REGISTRY}/${IMAGE_NAME}:latest" \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" \
        .
    
    if [ $? -eq 0 ]; then
        log_info "Image built successfully"
    else
        log_error "Image build failed"
        exit 1
    fi
}

# Test the image
test_image() {
    log_info "Testing the Docker image..."
    
    # Run a quick test to ensure the image starts correctly
    CONTAINER_ID=$(docker run -d --rm -p 8084:8084 "$FULL_IMAGE_NAME")
    
    # Wait for the application to start
    sleep 30
    
    # Check if the health endpoint is accessible
    if curl -f http://localhost:8084/actuator/health &> /dev/null; then
        log_info "Image test passed - health endpoint is accessible"
    else
        log_warn "Image test failed - health endpoint is not accessible"
    fi
    
    # Stop the test container
    docker stop "$CONTAINER_ID" &> /dev/null || true
}

# Push image to registry
push_image() {
    log_info "Pushing image to registry: $FULL_IMAGE_NAME"
    
    # Push versioned tag
    docker push "$FULL_IMAGE_NAME"
    
    # Push latest tag
    docker push "${REGISTRY}/${IMAGE_NAME}:latest"
    
    if [ $? -eq 0 ]; then
        log_info "Image pushed successfully"
    else
        log_error "Image push failed"
        exit 1
    fi
}

# Clean up local images
cleanup() {
    log_info "Cleaning up local images..."
    
    # Remove dangling images
    docker image prune -f &> /dev/null || true
    
    log_info "Cleanup completed"
}

# Show image info
show_info() {
    log_info "Image information:"
    docker images | grep "$IMAGE_NAME" | head -5
    
    log_info "Image size:"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "$IMAGE_NAME"
}

# Main script
main() {
    case "${1:-build}" in
        "build")
            log_info "Building Docker image..."
            check_docker
            build_image
            show_info
            log_info "Build completed successfully!"
            log_info "To test the image, run: $0 test"
            log_info "To push the image, run: $0 push"
            ;;
        "test")
            log_info "Testing Docker image..."
            check_docker
            test_image
            ;;
        "push")
            log_info "Pushing Docker image..."
            check_docker
            push_image
            log_info "Push completed successfully!"
            ;;
        "all")
            log_info "Building, testing, and pushing Docker image..."
            check_docker
            build_image
            test_image
            push_image
            cleanup
            show_info
            log_info "All operations completed successfully!"
            ;;
        "cleanup")
            check_docker
            cleanup
            ;;
        "info")
            show_info
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [COMMAND]"
            echo ""
            echo "Commands:"
            echo "  build      Build the Docker image (default)"
            echo "  test       Test the Docker image"
            echo "  push       Push the image to registry"
            echo "  all        Build, test, and push the image"
            echo "  cleanup    Clean up dangling images"
            echo "  info       Show image information"
            echo "  help       Show this help message"
            echo ""
            echo "Configuration:"
            echo "  Image: $FULL_IMAGE_NAME"
            echo "  Registry: $REGISTRY"
            echo ""
            echo "Examples:"
            echo "  $0 build"
            echo "  $0 test"
            echo "  $0 push"
            echo "  $0 all"
            ;;
        *)
            log_error "Unknown command: $1"
            echo "Use '$0 help' for usage information."
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
