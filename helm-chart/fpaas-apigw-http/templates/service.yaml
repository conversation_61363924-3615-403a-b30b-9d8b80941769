apiVersion: v1
kind: Service
metadata:
  name: {{ include "fpaas-apigw-http.fullname" . }}
  labels:
    {{- include "fpaas-apigw-http.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "fpaas-apigw-http.selectorLabels" . | nindent 4 }}
