# FPAAS API Gateway HTTP - Kubernetes 部署指南

本指南将帮助您使用 Helm 将 FPAAS API Gateway HTTP 应用部署到 Kubernetes 集群，使用 `application-fat` 配置。

## 前置条件

### 必需软件
- Kubernetes 1.19+
- Helm 3.2.0+
- Docker (用于构建镜像)
- kubectl (已配置连接到目标集群)

### 外部依赖
- MySQL 数据库
- Redis 服务器
- Nacos 服务注册中心
- Kafka 消息队列

## 快速开始

### 1. 构建 Docker 镜像

首先，您需要构建应用的 Docker 镜像：

```bash
# 更新 build-image.sh 中的镜像仓库地址
vim build-image.sh  # 修改 REGISTRY 变量

# 构建镜像
./build-image.sh build

# 测试镜像
./build-image.sh test

# 推送到镜像仓库
./build-image.sh push
```

### 2. 配置部署参数

编辑 `helm-chart/values-fat.yaml` 文件，更新以下配置：

```yaml
# 更新镜像仓库地址
image:
  repository: your-registry.com/fpaas-apigw-http
  tag: "2.6.1"

# 更新外部依赖地址
external:
  mysql:
    host: "your-mysql-host.com"
  redis:
    host: "your-redis-host.com"
  nacos:
    host: "your-nacos-host.com"
  kafka:
    host: "your-kafka-host.com"

# 更新 Ingress 域名
ingress:
  hosts:
    - host: fpaas-apigw-fat.yourdomain.com
```

### 3. 部署应用

使用提供的部署脚本：

```bash
cd helm-chart
./deploy.sh deploy
```

或者手动使用 Helm：

```bash
# 创建命名空间
kubectl create namespace fpaas-system

# 部署应用
helm install fpaas-apigw-http ./fpaas-apigw-http \
  -f values-fat.yaml \
  -n fpaas-system \
  --wait
```

## 详细配置说明

### 应用配置

应用使用 `application-fat.yml` 配置文件，主要配置项包括：

- **Spring Profile**: `fat`
- **数据库**: MySQL (fpaas 数据库)
- **缓存**: Redis
- **服务注册**: Nacos
- **消息队列**: Kafka
- **监控**: Actuator + Prometheus

### 资源配置

默认资源配置（可在 values-fat.yaml 中调整）：

```yaml
resources:
  limits:
    cpu: 3000m
    memory: 3Gi
  requests:
    cpu: 1000m
    memory: 1.5Gi

jvm:
  xms: "1536m"
  xmx: "1536m"
  xmn: "384m"
```

### 自动扩缩容

启用了 HPA（水平 Pod 自动扩缩容）：

```yaml
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 8
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 75
```

## 部署验证

### 1. 检查 Pod 状态

```bash
kubectl get pods -n fpaas-system -l app.kubernetes.io/name=fpaas-apigw-http
```

### 2. 检查服务状态

```bash
kubectl get svc -n fpaas-system -l app.kubernetes.io/name=fpaas-apigw-http
```

### 3. 查看应用日志

```bash
kubectl logs -f deployment/fpaas-apigw-http -n fpaas-system
```

### 4. 健康检查

```bash
# 端口转发
kubectl port-forward svc/fpaas-apigw-http 8084:8084 -n fpaas-system

# 检查健康状态
curl http://localhost:8084/actuator/health

# 检查应用信息
curl http://localhost:8084/actuator/info

# 检查 Prometheus 指标
curl http://localhost:8084/actuator/prometheus
```

## 常用操作

### 升级应用

```bash
# 使用脚本升级
./deploy.sh deploy

# 或手动升级
helm upgrade fpaas-apigw-http ./fpaas-apigw-http \
  -f values-fat.yaml \
  -n fpaas-system
```

### 回滚应用

```bash
# 使用脚本回滚
./deploy.sh rollback

# 或手动回滚
helm rollback fpaas-apigw-http -n fpaas-system
```

### 扩缩容

```bash
# 手动扩容到 5 个副本
kubectl scale deployment fpaas-apigw-http --replicas=5 -n fpaas-system

# 或修改 values-fat.yaml 中的 replicaCount 后重新部署
```

### 查看部署历史

```bash
helm history fpaas-apigw-http -n fpaas-system
```

## 监控和日志

### Prometheus 监控

应用暴露了 Prometheus 指标端点：`/actuator/prometheus`

可以配置 Prometheus 抓取这些指标：

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'fpaas-apigw-http'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
```

### 日志收集

应用日志存储在 `/app/logs` 目录，通过 PVC 持久化。

可以配置 Fluentd 或 Filebeat 收集日志：

```yaml
# 日志收集配置示例
- type: kubernetes
  paths:
    - /var/log/containers/fpaas-apigw-http-*.log
```

## 故障排查

### 常见问题

1. **Pod 启动失败**
   ```bash
   kubectl describe pod <pod-name> -n fpaas-system
   kubectl logs <pod-name> -n fpaas-system
   ```

2. **数据库连接失败**
   - 检查数据库地址和端口
   - 验证数据库用户名和密码
   - 确认网络连通性

3. **Redis 连接失败**
   - 检查 Redis 地址和端口
   - 验证 Redis 密码
   - 确认网络连通性

4. **Nacos 注册失败**
   - 检查 Nacos 地址和端口
   - 验证命名空间配置
   - 确认网络连通性

### 调试命令

```bash
# 进入 Pod 调试
kubectl exec -it <pod-name> -n fpaas-system -- /bin/bash

# 查看配置
kubectl get configmap fpaas-apigw-http-config -n fpaas-system -o yaml

# 查看密钥
kubectl get secret fpaas-apigw-http-secret -n fpaas-system -o yaml

# 查看事件
kubectl get events -n fpaas-system --sort-by='.lastTimestamp'
```

## 安全建议

1. **密钥管理**: 使用 Kubernetes Secrets 或外部密钥管理系统
2. **网络策略**: 配置 NetworkPolicy 限制网络访问
3. **RBAC**: 配置适当的角色和权限
4. **镜像安全**: 定期扫描镜像漏洞
5. **资源限制**: 设置适当的资源限制防止资源耗尽

## 生产环境注意事项

1. **高可用**: 确保至少 2 个副本
2. **资源监控**: 监控 CPU、内存使用情况
3. **日志轮转**: 配置日志轮转防止磁盘满
4. **备份**: 定期备份配置和数据
5. **更新策略**: 使用滚动更新策略

## 支持

如有问题，请联系 FPAAS 团队或在项目仓库中创建 issue。
